import React, { useState, useRef } from 'react';
import { TbUser, TbUsers, Tb<PERSON>rophy, TbPlayerPlay, TbPlayerPause, Tb<PERSON>lock, TbSearch, TbFilter } from 'react-icons/tb';
import UserRankingCard from './UserRankingCard';

const UserRankingList = ({
    users = [],
    currentUserId = null,
    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'
    size = 'medium',
    showStats = true,
    className = '',
    currentUserRef = null,
    showFindMe = false,
    lastUpdated = null,
    autoRefresh = false,
    onAutoRefreshToggle = null
}) => {
    // State for search and filtering
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all'); // 'all', 'premium', 'free', 'expired'
    const [sortBy, setSortBy] = useState('rank'); // 'rank', 'xp', 'name'
    const [localShowFindMe, setLocalShowFindMe] = useState(false);
    const localCurrentUserRef = useRef(null);

    // Use passed refs or local ones
    const userRef = currentUserRef || localCurrentUserRef;
    const findMeActive = showFindMe || localShowFindMe;

    // Filter and search users
    const filteredUsers = users.filter(user => {
        // Search filter
        const matchesSearch = user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            user.email?.toLowerCase().includes(searchTerm.toLowerCase());

        // Subscription filter
        const userStatus = user.subscriptionStatus?.toLowerCase() || 'free';
        let matchesFilter = true;

        switch (filterType) {
            case 'premium':
                matchesFilter = userStatus === 'premium' || userStatus === 'active';
                break;
            case 'expired':
                matchesFilter = userStatus === 'expired';
                break;
            case 'free':
                matchesFilter = userStatus === 'free';
                break;
            default:
                matchesFilter = true;
        }

        return matchesSearch && matchesFilter;
    }).sort((a, b) => {
        switch (sortBy) {
            case 'xp':
                return (b.totalXP || 0) - (a.totalXP || 0);
            case 'name':
                return (a.name || '').localeCompare(b.name || '');
            default:
                return (a.rank || 0) - (b.rank || 0);
        }
    });

    // Calculate class ranks for filtered users
    const usersWithClassRank = filteredUsers.map(user => {
        // Group users by class and calculate class rank
        const sameClassUsers = filteredUsers.filter(u => u.class === user.class);
        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;
        return { ...user, classRank };
    });

    // Find current user
    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);

    // Scroll to current user
    const scrollToCurrentUser = () => {
        if (userRef.current) {
            userRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            setLocalShowFindMe(true);
            // Hide the highlight after 3 seconds
            setTimeout(() => setLocalShowFindMe(false), 3000);
        }
    };

    // Get layout classes
    const getLayoutClasses = () => {
        switch (layout) {
            case 'vertical':
                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';
            case 'grid':
                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
            case 'horizontal':
            default:
                return 'space-y-3';
        }
    };



    // Stats summary with enhanced calculations
    const totalUsers = users.length;
    const premiumUsers = users.filter(u =>
        u.subscriptionStatus === 'active' ||
        u.subscriptionStatus === 'premium' ||
        u.normalizedSubscriptionStatus === 'premium'
    ).length;

    // Use ranking score or XP as the primary metric
    const topScore = users.length > 0 ? Math.max(...users.map(u =>
        u.rankingScore || u.totalXP || u.totalPoints || 0
    )) : 0;

    // Calculate additional stats
    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;
    const averageXP = users.length > 0 ?
        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header with Stats */}
            {showStats && (
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200 animate-fadeInUp">
                    {/* Search and Filter Section */}
                    <div className="mb-6 bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-white/50">
                        <div className="flex flex-col sm:flex-row gap-4">
                            {/* Search Input */}
                            <div className="flex-1 relative">
                                <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <input
                                    type="text"
                                    placeholder="Search users by name or email..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                />
                            </div>

                            {/* Filter Dropdown */}
                            <div className="relative">
                                <TbFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                                <select
                                    value={filterType}
                                    onChange={(e) => setFilterType(e.target.value)}
                                    className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                                >
                                    <option value="all">All Users</option>
                                    <option value="premium">Premium</option>
                                    <option value="free">Free</option>
                                    <option value="expired">Expired</option>
                                </select>
                            </div>

                            {/* Sort Dropdown */}
                            <select
                                value={sortBy}
                                onChange={(e) => setSortBy(e.target.value)}
                                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white"
                            >
                                <option value="rank">Sort by Rank</option>
                                <option value="xp">Sort by XP</option>
                                <option value="name">Sort by Name</option>
                            </select>
                        </div>

                        {/* Results Count */}
                        <div className="mt-3 text-sm text-gray-600">
                            Showing {usersWithClassRank.length} of {users.length} users
                            {searchTerm && ` matching "${searchTerm}"`}
                            {filterType !== 'all' && ` (${filterType} only)`}
                        </div>
                    </div>
                    <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center space-x-4">
                            <div className="flex items-center space-x-3">
                                <div className="p-3 bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 rounded-xl shadow-lg">
                                    <TbTrophy className="w-7 h-7 text-white" />
                                </div>
                                <div>
                                    <h2 className="text-3xl font-black text-gray-900 bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
                                        Leaderboard
                                    </h2>
                                    <p className="text-sm text-gray-600 font-medium">Top performers across all levels</p>
                                </div>
                            </div>

                            {lastUpdated && (
                                <div className="flex items-center space-x-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                                    <TbClock className="w-4 h-4 text-blue-600" />
                                    <span className="text-sm text-blue-700 font-medium">
                                        Updated {new Date(lastUpdated).toLocaleTimeString()}
                                    </span>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center space-x-2">
                            {/* Auto-refresh toggle */}
                            {onAutoRefreshToggle && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={onAutoRefreshToggle}
                                    className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${
                                        autoRefresh
                                            ? 'bg-green-500 hover:bg-green-600 text-white'
                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                                    }`}
                                >
                                    {autoRefresh ? <TbPlayerPause className="w-4 h-4" /> : <TbPlayerPlay className="w-4 h-4" />}
                                    <span className="hidden sm:inline">
                                        {autoRefresh ? 'Auto' : 'Manual'}
                                    </span>
                                </motion.button>
                            )}



                            {/* Find Me button */}
                            {currentUserId && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={scrollToCurrentUser}
                                    className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                                >
                                    <TbUser className="w-4 h-4" />
                                    <span className="hidden sm:inline">Find Me</span>
                                </motion.button>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-5 border border-blue-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="p-2 bg-blue-500 rounded-lg">
                                    <TbUsers className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-sm font-semibold text-blue-700">Total Users</span>
                            </div>
                            <div className="text-3xl font-black text-blue-900 mb-1">{totalUsers}</div>
                            <div className="text-xs text-blue-600 font-medium">{activeUsers} active</div>
                        </div>

                        <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-xl p-5 border border-yellow-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
                                    <TbTrophy className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-sm font-semibold text-yellow-700">Premium Users</span>
                            </div>
                            <div className="text-3xl font-black text-yellow-900 mb-1">{premiumUsers}</div>
                            <div className="text-xs text-yellow-600 font-medium">
                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium
                            </div>
                        </div>

                        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-5 border border-green-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="p-2 bg-green-500 rounded-lg">
                                    <TbUser className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-sm font-semibold text-green-700">Top Score</span>
                            </div>
                            <div className="text-3xl font-black text-green-900 mb-1">{topScore.toLocaleString()}</div>
                            <div className="text-xs text-green-600 font-medium">ranking points</div>
                        </div>

                        <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-5 border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                            <div className="flex items-center space-x-3 mb-3">
                                <div className="p-2 bg-purple-500 rounded-lg">
                                    <TbTrophy className="w-5 h-5 text-white" />
                                </div>
                                <span className="text-sm font-semibold text-purple-700">Avg XP</span>
                            </div>
                            <div className="text-3xl font-black text-purple-900 mb-1">{averageXP.toLocaleString()}</div>
                            <div className="text-xs text-gray-500 mt-1">experience points</div>
                        </div>
                    </div>
                </div>
            )}

            {/* User List */}
            <div className={`animate-fadeInUp ${getLayoutClasses()}`}>
                {usersWithClassRank.map((user, index) => {
                    const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;
                    const rank = user.rank || index + 1;

                    return (
                        <div
                            key={user.userId || user._id}
                            ref={isCurrentUser ? userRef : null}
                            className={`animate-slideInLeft transition-all duration-300 ${
                                isCurrentUser && findMeActive
                                    ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'
                                    : isCurrentUser
                                    ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'
                                        : ''
                                }`}
                            >
                                <UserRankingCard
                                    user={user}
                                    rank={rank}
                                    classRank={user.classRank}
                                    isCurrentUser={isCurrentUser}
                                    layout={layout}
                                    size={size}
                                    showStats={showStats}
                                />
                            </div>
                        );
                    })}
            </div>

            {/* Empty State */}
            {usersWithClassRank.length === 0 && (
                <div className="text-center py-12 bg-white rounded-xl border border-gray-200 animate-fadeInUp">
                    <TbUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No users found</h3>
                    <p className="text-gray-500">
                        {users.length === 0 ? 'No ranking data available.' : 'Try adjusting your search or filter criteria.'}
                    </p>
                </div>
            )}

            {/* Floating Action Button for Current User */}
            {currentUserId && usersWithClassRank.length > 10 && (
                <button
                    onClick={scrollToCurrentUser}
                    className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50 hover:scale-110 active:scale-95 animate-bounce"
                    title="Find me in ranking"
                >
                    <TbUser className="w-6 h-6" />
                </button>
            )}
        </div>
    );
};

export default UserRankingList;
