import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { TbUser, TbUsers, TbTrophy, TbRefresh, TbPlayerPlay, TbPlayerPause, TbClock } from 'react-icons/tb';
import UserRankingCard from './UserRankingCard';

const UserRankingList = ({
    users = [],
    currentUserId = null,
    layout = 'horizontal', // 'horizontal', 'vertical', 'grid'
    size = 'medium',
    showStats = true,
    className = '',
    currentUserRef = null,
    showFindMe = false,
    onRefresh = null,
    lastUpdated = null,
    autoRefresh = false,
    onAutoRefreshToggle = null
}) => {
    const [localShowFindMe, setLocalShowFindMe] = useState(false);
    const localCurrentUserRef = useRef(null);

    // Use passed refs or local ones
    const userRef = currentUserRef || localCurrentUserRef;
    const findMeActive = showFindMe || localShowFindMe;

    // Sort users by rank (no filtering)
    const sortedUsers = users.sort((a, b) => (a.rank || 0) - (b.rank || 0));

    // Calculate class ranks
    const usersWithClassRank = sortedUsers.map(user => {
        // Group users by class and calculate class rank
        const sameClassUsers = sortedUsers.filter(u => u.class === user.class);
        const classRank = sameClassUsers.findIndex(u => u._id === user._id || u.userId === user.userId) + 1;
        return { ...user, classRank };
    });

    // Find current user
    const currentUser = usersWithClassRank.find(user => user._id === currentUserId || user.userId === currentUserId);

    // Scroll to current user
    const scrollToCurrentUser = () => {
        if (userRef.current) {
            userRef.current.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            setLocalShowFindMe(true);
            // Hide the highlight after 3 seconds
            setTimeout(() => setLocalShowFindMe(false), 3000);
        }
    };

    // Get layout classes
    const getLayoutClasses = () => {
        switch (layout) {
            case 'vertical':
                return 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4';
            case 'grid':
                return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
            case 'horizontal':
            default:
                return 'space-y-3';
        }
    };

    // Container animation variants
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };

    // Stats summary with enhanced calculations
    const totalUsers = users.length;
    const premiumUsers = users.filter(u =>
        u.subscriptionStatus === 'active' ||
        u.subscriptionStatus === 'premium' ||
        u.normalizedSubscriptionStatus === 'premium'
    ).length;

    // Use ranking score or XP as the primary metric
    const topScore = users.length > 0 ? Math.max(...users.map(u =>
        u.rankingScore || u.totalXP || u.totalPoints || 0
    )) : 0;

    // Calculate additional stats
    const activeUsers = users.filter(u => (u.totalQuizzesTaken || 0) > 0).length;
    const averageXP = users.length > 0 ?
        Math.round(users.reduce((sum, u) => sum + (u.totalXP || 0), 0) / users.length) : 0;

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header with Stats */}
            {showStats && (
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200"
                >
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                            <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
                                <TbTrophy className="w-6 h-6 text-yellow-500" />
                                <span>Leaderboard</span>
                            </h2>

                            {lastUpdated && (
                                <div className="flex items-center space-x-1 text-sm text-gray-500">
                                    <TbClock className="w-4 h-4" />
                                    <span>Updated {new Date(lastUpdated).toLocaleTimeString()}</span>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center space-x-2">
                            {/* Auto-refresh toggle */}
                            {onAutoRefreshToggle && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={onAutoRefreshToggle}
                                    className={`px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${
                                        autoRefresh
                                            ? 'bg-green-500 hover:bg-green-600 text-white'
                                            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                                    }`}
                                >
                                    {autoRefresh ? <TbPlayerPause className="w-4 h-4" /> : <TbPlayerPlay className="w-4 h-4" />}
                                    <span className="hidden sm:inline">
                                        {autoRefresh ? 'Auto' : 'Manual'}
                                    </span>
                                </motion.button>
                            )}

                            {/* Manual refresh button */}
                            {onRefresh && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={onRefresh}
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                                >
                                    <TbRefresh className="w-4 h-4" />
                                    <span className="hidden sm:inline">Refresh</span>
                                </motion.button>
                            )}

                            {/* Find Me button */}
                            {currentUserId && (
                                <motion.button
                                    whileHover={{ scale: 1.05 }}
                                    whileTap={{ scale: 0.95 }}
                                    onClick={scrollToCurrentUser}
                                    className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2"
                                >
                                    <TbUser className="w-4 h-4" />
                                    <span className="hidden sm:inline">Find Me</span>
                                </motion.button>
                            )}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbUsers className="w-5 h-5 text-blue-500" />
                                <span className="text-sm text-gray-600">Total Users</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{totalUsers}</div>
                            <div className="text-xs text-gray-500 mt-1">{activeUsers} active</div>
                        </div>

                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbTrophy className="w-5 h-5 text-yellow-500" />
                                <span className="text-sm text-gray-600">Premium Users</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{premiumUsers}</div>
                            <div className="text-xs text-gray-500 mt-1">
                                {totalUsers > 0 ? Math.round((premiumUsers / totalUsers) * 100) : 0}% premium
                            </div>
                        </div>

                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbUser className="w-5 h-5 text-green-500" />
                                <span className="text-sm text-gray-600">Top Score</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{topScore.toLocaleString()}</div>
                            <div className="text-xs text-gray-500 mt-1">ranking points</div>
                        </div>

                        <div className="bg-white rounded-lg p-4 border border-gray-200">
                            <div className="flex items-center space-x-2">
                                <TbTrophy className="w-5 h-5 text-purple-500" />
                                <span className="text-sm text-gray-600">Avg XP</span>
                            </div>
                            <div className="text-2xl font-bold text-gray-900 mt-1">{averageXP.toLocaleString()}</div>
                            <div className="text-xs text-gray-500 mt-1">experience points</div>
                        </div>
                    </div>
                </motion.div>
            )}



            {/* User List */}
            <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className={getLayoutClasses()}
            >
                <AnimatePresence>
                    {usersWithClassRank.map((user, index) => {
                        const isCurrentUser = user.userId === currentUserId || user._id === currentUserId;
                        const rank = user.rank || index + 1;

                        return (
                            <motion.div
                                key={user.userId || user._id}
                                ref={isCurrentUser ? userRef : null}
                                layout
                                initial={{ opacity: 0, scale: 0.9 }}
                                animate={{ opacity: 1, scale: 1 }}
                                exit={{ opacity: 0, scale: 0.9 }}
                                transition={{ duration: 0.2 }}
                                className={`${
                                    isCurrentUser && findMeActive
                                        ? 'find-me-highlight ring-4 ring-yellow-400 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl shadow-2xl'
                                        : isCurrentUser
                                        ? 'ring-2 ring-blue-400 bg-blue-50/50 rounded-lg'
                                        : ''
                                }`}
                            >
                                <UserRankingCard
                                    user={user}
                                    rank={rank}
                                    classRank={user.classRank}
                                    isCurrentUser={isCurrentUser}
                                    layout={layout}
                                    size={size}
                                    showStats={showStats}
                                />
                            </motion.div>
                        );
                    })}
                </AnimatePresence>
            </motion.div>

            {/* Empty State */}
            {usersWithClassRank.length === 0 && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-center py-12"
                >
                    <TbUsers className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                    <p className="text-gray-500">
                        No users available to display
                    </p>
                </motion.div>
            )}

            {/* Floating Action Button for Current User */}
            {currentUserId && usersWithClassRank.length > 10 && (
                <motion.button
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={scrollToCurrentUser}
                    className="fixed bottom-6 right-6 bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 z-50"
                    title="Find me in ranking"
                >
                    <TbUser className="w-6 h-6" />
                </motion.button>
            )}
        </div>
    );
};

export default UserRankingList;
