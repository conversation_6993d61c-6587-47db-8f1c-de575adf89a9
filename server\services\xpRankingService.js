const User = require("../models/userModel");
const XPTransaction = require("../models/xpTransactionModel");
const LevelDefinition = require("../models/levelDefinitionModel");
const Report = require("../models/reportModel");

class XPRankingService {
  constructor() {
    // Ranking weight configuration
    this.weights = {
      totalXP: 1.0,           // Base XP weight
      currentLevel: 100,      // Level bonus (100 XP per level)
      averageScore: 2.0,      // Average score multiplier
      streakBonus: 10,        // Streak bonus (10 XP per streak)
      achievementBonus: 25,   // Achievement bonus (25 XP per achievement)
      activityBonus: 0.5,     // Recent activity bonus
      premiumBonus: 50,       // Premium user bonus
    };

    // Seasonal ranking configuration
    this.seasonConfig = {
      currentSeason: "2024-S1",
      seasonWeight: 0.3,      // 30% weight for seasonal XP
      lifetimeWeight: 0.7,    // 70% weight for lifetime XP
    };
  }

  /**
   * Calculate comprehensive ranking score for a user
   */
  async calculateRankingScore(user) {
    try {
      // Base XP score
      const baseXPScore = (user.totalXP || 0) * this.weights.totalXP;
      
      // Level bonus
      const levelBonus = (user.currentLevel || 1) * this.weights.currentLevel;
      
      // Average score bonus
      const averageScoreBonus = (user.averageScore || 0) * this.weights.averageScore;
      
      // Streak bonus
      const streakBonus = (user.bestStreak || 0) * this.weights.streakBonus;
      
      // Achievement bonus
      const achievementCount = user.achievements ? user.achievements.length : 0;
      const achievementBonus = achievementCount * this.weights.achievementBonus;
      
      // Recent activity bonus (last 7 days)
      const activityBonus = await this.calculateActivityBonus(user._id);
      
      // Premium user bonus
      const premiumBonus = this.isPremiumUser(user) ? this.weights.premiumBonus : 0;
      
      // Seasonal vs Lifetime XP weighting
      const seasonalScore = (user.seasonXP || 0) * this.seasonConfig.seasonWeight;
      const lifetimeScore = (user.lifetimeXP || 0) * this.seasonConfig.lifetimeWeight;
      const weightedXPScore = seasonalScore + lifetimeScore;
      
      // Calculate final ranking score
      const rankingScore = Math.round(
        weightedXPScore +
        levelBonus +
        averageScoreBonus +
        streakBonus +
        achievementBonus +
        activityBonus +
        premiumBonus
      );

      return {
        rankingScore: rankingScore,
        breakdown: {
          baseXP: Math.round(baseXPScore),
          weightedXP: Math.round(weightedXPScore),
          levelBonus: Math.round(levelBonus),
          averageScoreBonus: Math.round(averageScoreBonus),
          streakBonus: Math.round(streakBonus),
          achievementBonus: Math.round(achievementBonus),
          activityBonus: Math.round(activityBonus),
          premiumBonus: Math.round(premiumBonus),
        }
      };

    } catch (error) {
      console.error('Error calculating ranking score:', error);
      return { rankingScore: 0, breakdown: {} };
    }
  }

  /**
   * Calculate recent activity bonus
   */
  async calculateActivityBonus(userId) {
    try {
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentTransactions = await XPTransaction.find({
        user: userId,
        createdAt: { $gte: sevenDaysAgo }
      });

      const recentXP = recentTransactions.reduce((sum, transaction) => 
        sum + transaction.xpAmount, 0
      );

      return recentXP * this.weights.activityBonus;

    } catch (error) {
      console.error('Error calculating activity bonus:', error);
      return 0;
    }
  }

  /**
   * Check if user is premium
   */
  isPremiumUser(user) {
    const premiumStatuses = ['premium', 'active'];
    const hasValidSubscription = premiumStatuses.includes(user.subscriptionStatus);
    
    if (!hasValidSubscription) return false;
    
    // Check subscription end date if it exists
    if (user.subscriptionEndDate) {
      return new Date(user.subscriptionEndDate) > new Date();
    }
    
    return true;
  }

  /**
   * Get comprehensive leaderboard with XP-based ranking
   */
  async getXPLeaderboard(options = {}) {
    try {
      const {
        limit = 100,
        classFilter = null,
        levelFilter = null,
        seasonFilter = null,
        includeInactive = false
      } = options;

      // Build match criteria - exclude admins and blocked users
      const matchCriteria = {
        isAdmin: { $ne: true },
        isBlocked: { $ne: true }
      };

      if (classFilter) {
        matchCriteria.class = classFilter;
      }

      if (levelFilter) {
        // Handle both capitalized and lowercase level values
        const normalizedLevel = levelFilter.toLowerCase();
        matchCriteria.$or = [
          { level: levelFilter },
          { level: normalizedLevel },
          { level: levelFilter.charAt(0).toUpperCase() + levelFilter.slice(1).toLowerCase() }
        ];
      }

      if (seasonFilter) {
        matchCriteria.currentSeason = seasonFilter;
      }

      if (!includeInactive) {
        // Include users with either quiz activity or XP
        matchCriteria.$or = [
          { totalQuizzesTaken: { $gt: 0 } },
          { totalXP: { $gt: 0 } }
        ];
      }

      // Aggregation pipeline for enhanced ranking
      const pipeline = [
        { $match: matchCriteria },

        // Add computed fields for better ranking calculation
        {
          $addFields: {
            // Ensure all numeric fields have default values
            totalXP: { $ifNull: ["$totalXP", 0] },
            currentLevel: { $ifNull: ["$currentLevel", 1] },
            averageScore: { $ifNull: ["$averageScore", 0] },
            bestStreak: { $ifNull: ["$bestStreak", 0] },
            currentStreak: { $ifNull: ["$currentStreak", 0] },
            totalQuizzesTaken: { $ifNull: ["$totalQuizzesTaken", 0] },
            totalPointsEarned: { $ifNull: ["$totalPointsEarned", 0] },
            lifetimeXP: { $ifNull: ["$lifetimeXP", "$totalXP"] },
            seasonXP: { $ifNull: ["$seasonXP", { $multiply: ["$totalXP", 0.3] }] },

            // Normalize subscription status
            normalizedSubscriptionStatus: {
              $switch: {
                branches: [
                  { case: { $in: ["$subscriptionStatus", ["active", "premium"]] }, then: "premium" },
                  { case: { $eq: ["$subscriptionStatus", "free"] }, then: "free" },
                  { case: { $eq: ["$subscriptionStatus", "expired"] }, then: "expired" }
                ],
                default: "free"
              }
            },

            // Calculate achievement count
            achievementCount: {
              $cond: {
                if: { $isArray: "$achievements" },
                then: { $size: "$achievements" },
                else: 0
              }
            }
          }
        },
        
        // Calculate ranking score components
        {
          $addFields: {
            // Base XP calculation
            baseXPScore: { $multiply: ["$totalXP", this.weights.totalXP] },

            // Level bonus
            levelBonus: { $multiply: ["$currentLevel", this.weights.currentLevel] },

            // Average score bonus
            averageScoreBonus: { $multiply: ["$averageScore", this.weights.averageScore] },

            // Streak bonus
            streakBonus: { $multiply: ["$bestStreak", this.weights.streakBonus] },

            // Achievement bonus
            achievementBonus: { $multiply: ["$achievementCount", this.weights.achievementBonus] },

            // Premium bonus
            premiumBonus: {
              $cond: [
                { $eq: ["$normalizedSubscriptionStatus", "premium"] },
                this.weights.premiumBonus,
                0
              ]
            },

            // Activity bonus (based on recent quiz activity)
            activityBonus: {
              $cond: [
                { $gte: ["$totalQuizzesTaken", 5] },
                { $multiply: ["$totalQuizzesTaken", this.weights.activityBonus] },
                0
              ]
            },

            // Weighted XP (seasonal + lifetime)
            weightedXP: {
              $add: [
                { $multiply: ["$seasonXP", this.seasonConfig.seasonWeight] },
                { $multiply: ["$lifetimeXP", this.seasonConfig.lifetimeWeight] }
              ]
            }
          }
        },
        
        // Calculate final ranking score
        {
          $addFields: {
            rankingScore: {
              $round: {
                $add: [
                  "$weightedXP",
                  "$levelBonus",
                  "$averageScoreBonus",
                  "$streakBonus",
                  "$achievementBonus",
                  "$activityBonus",
                  "$premiumBonus"
                ]
              }
            }
          }
        },
        
        // Sort by ranking score
        { $sort: { rankingScore: -1, totalXP: -1, name: 1 } },
        
        // Limit results
        { $limit: limit },
        
        // Add rank field
        {
          $group: {
            _id: null,
            users: { $push: "$$ROOT" }
          }
        },
        {
          $unwind: {
            path: "$users",
            includeArrayIndex: "rank"
          }
        },
        {
          $addFields: {
            "users.rank": { $add: ["$rank", 1] }
          }
        },
        {
          $replaceRoot: { newRoot: "$users" }
        },
        
        // Project final fields
        {
          $project: {
            _id: 1,
            userId: "$_id", // Add userId field for compatibility
            name: 1,
            class: 1,
            level: 1,
            school: 1,
            profileImage: 1,
            profilePicture: "$profileImage", // Add alias for compatibility
            email: 1,

            // Subscription Info
            subscriptionStatus: "$normalizedSubscriptionStatus",
            subscriptionEndDate: 1,
            subscriptionPlan: 1,

            // XP and Level Info
            totalXP: 1,
            currentLevel: 1,
            xpToNextLevel: 1,
            seasonXP: 1,
            lifetimeXP: 1,

            // Legacy Points (for backward compatibility)
            totalPoints: "$totalPointsEarned",
            totalPointsEarned: 1,

            // Statistics
            totalQuizzesTaken: 1,
            quizzesTaken: "$totalQuizzesTaken", // Add alias
            averageScore: 1,
            bestStreak: 1,
            currentStreak: 1,
            achievements: 1,
            achievementCount: 1,

            // Additional stats for UI
            passedExamsCount: { $ifNull: ["$passedExamsCount", 0] },
            retryCount: { $ifNull: ["$retryCount", 0] },

            // Ranking Info
            rank: 1,
            rankingScore: 1,
            score: "$rankingScore", // Add alias for compatibility

            // Score Breakdown
            breakdown: {
              baseXP: "$baseXPScore",
              weightedXP: "$weightedXP",
              levelBonus: "$levelBonus",
              averageScoreBonus: "$averageScoreBonus",
              streakBonus: "$streakBonus",
              achievementBonus: "$achievementBonus",
              activityBonus: "$activityBonus",
              premiumBonus: "$premiumBonus"
            },

            // Timestamps
            createdAt: 1,
            updatedAt: 1
          }
        }
      ];

      const leaderboard = await User.aggregate(pipeline);
      
      return {
        success: true,
        data: leaderboard,
        metadata: {
          totalUsers: leaderboard.length,
          season: this.seasonConfig.currentSeason,
          weights: this.weights,
          generatedAt: new Date()
        }
      };

    } catch (error) {
      console.error('Error generating XP leaderboard:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Get user's ranking position and nearby users
   */
  async getUserRankingPosition(userId, context = 5) {
    try {
      const leaderboard = await this.getXPLeaderboard({ limit: 10000 });
      
      if (!leaderboard.success) {
        throw new Error('Failed to generate leaderboard');
      }

      const userIndex = leaderboard.data.findIndex(user => 
        user._id.toString() === userId.toString()
      );

      if (userIndex === -1) {
        return {
          success: false,
          message: 'User not found in rankings'
        };
      }

      const userRank = userIndex + 1;
      const startIndex = Math.max(0, userIndex - context);
      const endIndex = Math.min(leaderboard.data.length, userIndex + context + 1);
      
      const nearbyUsers = leaderboard.data.slice(startIndex, endIndex);

      return {
        success: true,
        userRank: userRank,
        totalUsers: leaderboard.data.length,
        user: leaderboard.data[userIndex],
        nearbyUsers: nearbyUsers,
        context: {
          showingFrom: startIndex + 1,
          showingTo: endIndex,
          contextSize: context
        }
      };

    } catch (error) {
      console.error('Error getting user ranking position:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get class-specific rankings
   */
  async getClassRankings(className, limit = 50) {
    return this.getXPLeaderboard({
      limit: limit,
      classFilter: className
    });
  }

  /**
   * Get seasonal rankings
   */
  async getSeasonalRankings(season = null, limit = 100) {
    const targetSeason = season || this.seasonConfig.currentSeason;
    return this.getXPLeaderboard({
      limit: limit,
      seasonFilter: targetSeason
    });
  }
}

module.exports = new XPRankingService();
